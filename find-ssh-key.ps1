# PowerShell script to help find and display SSH private keys
# Run this script to locate your SSH private key for the app

Write-Host "🔍 SSH Private Key Finder" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

$sshDir = "$env:USERPROFILE\.ssh"

if (Test-Path $sshDir) {
    Write-Host "✅ Found SSH directory: $sshDir" -ForegroundColor Green
    Write-Host ""
    
    # List all files in .ssh directory
    Write-Host "📁 Files in SSH directory:" -ForegroundColor Yellow
    Get-ChildItem $sshDir | ForEach-Object {
        $isPrivateKey = $_.Name -match '^(id_rsa|id_ed25519|id_ecdsa)$' -and $_.Name -notmatch '\.pub$'
        $isPublicKey = $_.Name -match '\.pub$'
        
        if ($isPrivateKey) {
            Write-Host "🔑 $($_.Name) (PRIVATE KEY)" -ForegroundColor Green
        } elseif ($isPublicKey) {
            Write-Host "🔓 $($_.Name) (public key)" -ForegroundColor Blue
        } else {
            Write-Host "📄 $($_.Name)" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    
    # Find likely private key files
    $privateKeys = Get-ChildItem $sshDir | Where-Object { 
        $_.Name -match '^(id_rsa|id_ed25519|id_ecdsa)$' -and $_.Name -notmatch '\.pub$'
    }
    
    if ($privateKeys.Count -gt 0) {
        Write-Host "🎯 Found private key(s):" -ForegroundColor Green
        
        foreach ($key in $privateKeys) {
            Write-Host ""
            Write-Host "Key: $($key.Name)" -ForegroundColor Yellow
            Write-Host "Path: $($key.FullName)" -ForegroundColor Gray
            
            # Ask if user wants to display this key
            $response = Read-Host "Display content of $($key.Name)? (y/n)"
            
            if ($response -eq 'y' -or $response -eq 'Y') {
                Write-Host ""
                Write-Host "📋 Private Key Content (copy this to the app):" -ForegroundColor Cyan
                Write-Host "================================================" -ForegroundColor Cyan
                
                try {
                    $keyContent = Get-Content $key.FullName -Raw
                    Write-Host $keyContent -ForegroundColor White
                    
                    Write-Host "================================================" -ForegroundColor Cyan
                    Write-Host ""
                    
                    # Ask if user wants to copy to clipboard
                    $copyResponse = Read-Host "Copy to clipboard? (y/n)"
                    if ($copyResponse -eq 'y' -or $copyResponse -eq 'Y') {
                        $keyContent | Set-Clipboard
                        Write-Host "✅ Private key copied to clipboard!" -ForegroundColor Green
                        Write-Host "You can now paste it into the app's Private Key field." -ForegroundColor Green
                    }
                } catch {
                    Write-Host "❌ Error reading key file: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "❌ No standard private keys found (id_rsa, id_ed25519, id_ecdsa)" -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 You might have a custom-named key. Check the files listed above." -ForegroundColor Yellow
        Write-Host "   Private keys typically don't have the .pub extension." -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ SSH directory not found: $sshDir" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 This might mean:" -ForegroundColor Yellow
    Write-Host "   1. You haven't generated SSH keys yet" -ForegroundColor Yellow
    Write-Host "   2. Your keys are in a different location" -ForegroundColor Yellow
    Write-Host "   3. You're using a different SSH client" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Copy your private key content (shown above or from clipboard)" -ForegroundColor White
Write-Host "2. Open the Site Cloner Desktop app" -ForegroundColor White
Write-Host "3. Go to SSH Connection tab" -ForegroundColor White
Write-Host "4. Fill in the form:" -ForegroundColor White
Write-Host "   - Host: ssh.academy.psychscene.com" -ForegroundColor Gray
Write-Host "   - Port: 18765" -ForegroundColor Gray
Write-Host "   - Username: user" -ForegroundColor Gray
Write-Host "   - Authentication: Private Key" -ForegroundColor Gray
Write-Host "   - Private Key: [paste your key here]" -ForegroundColor Gray
Write-Host "5. Click 'Test Connection' then 'Connect'" -ForegroundColor White

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
