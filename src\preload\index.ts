import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// SSH API types
export interface SSHConnectionConfig {
  host: string
  port: number
  username: string
  password?: string
  privateKey?: Buffer | string
  passphrase?: string
  timeout?: number
}

// Custom APIs for renderer
const api = {
  ssh: {
    testConnection: (config: SSHConnectionConfig) =>
      ipcRenderer.invoke('ssh:test-connection', config),
    connect: (config: SSHConnectionConfig) => ipc<PERSON>enderer.invoke('ssh:connect', config),
    disconnect: () => ipc<PERSON>enderer.invoke('ssh:disconnect'),
    listDirectory: (remotePath?: string) => ipcRenderer.invoke('ssh:list-directory', remotePath),
    executeCommand: (command: string) => ipcRenderer.invoke('ssh:execute-command', command),
    getStatus: () => ipcRenderer.invoke('ssh:get-status')
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
