<script setup lang="ts">
import { ref } from 'vue'
import Versions from './components/Versions.vue'
import SSHConnectionForm from './components/connection/SSHConnectionForm.vue'

const currentTab = ref<'ssh' | 'about'>('ssh')

const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')
</script>

<template>
  <div class="app-container">
    <header class="app-header">
      <img alt="logo" class="logo" src="./assets/electron.svg" />
      <h1 class="app-title">Site Cloner Desktop</h1>
      <p class="app-subtitle">Clone and migrate websites to local development environment</p>
    </header>

    <nav class="app-nav">
      <button
        @click="currentTab = 'ssh'"
        :class="{ active: currentTab === 'ssh' }"
        class="nav-button"
      >
        SSH Connection
      </button>
      <button
        @click="currentTab = 'about'"
        :class="{ active: currentTab === 'about' }"
        class="nav-button"
      >
        About
      </button>
    </nav>

    <main class="app-main">
      <div v-if="currentTab === 'ssh'" class="tab-content">
        <SSHConnectionForm />
      </div>

      <div v-if="currentTab === 'about'" class="tab-content">
        <div class="about-section">
          <div class="creator">Powered by electron-vite</div>
          <div class="text">
            Build an Electron app with
            <span class="vue">Vue</span>
            and
            <span class="ts">TypeScript</span>
          </div>
          <p class="tip">Please try pressing <code>F12</code> to open the devTool</p>
          <div class="actions">
            <div class="action">
              <a href="https://electron-vite.org/" target="_blank" rel="noreferrer">Documentation</a>
            </div>
            <div class="action">
              <a target="_blank" rel="noreferrer" @click="ipcHandle">Send IPC</a>
            </div>
          </div>
          <Versions />
        </div>
      </div>
    </main>
  </div>
</template>
