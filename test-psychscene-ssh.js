// Quick test script for ssh.academy.psychscene.com
// Open browser console (F12) in the app and run this

async function testPsychSceneSSH() {
  console.log('🚀 Testing SSH connection to ssh.academy.psychscene.com...');
  
  // You need to replace 'YOUR_PRIVATE_KEY_HERE' with your actual private key
  const sshConfig = {
    host: 'ssh.academy.psychscene.com',
    port: 18765,
    username: 'user',
    privateKey: `-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_PRIVATE_KEY_HERE
-----END OPENSSH PRIVATE KEY-----`,
    timeout: 15 // Increased timeout for remote server
  };
  
  try {
    console.log('📡 Testing connection...');
    const testResult = await window.api.ssh.testConnection(sshConfig);
    
    if (testResult.success) {
      console.log('✅ Connection test successful!');
      console.log('🖥️ Server Info:', testResult.serverInfo);
      
      console.log('🔗 Establishing persistent connection...');
      const connectResult = await window.api.ssh.connect(sshConfig);
      
      if (connectResult.success) {
        console.log('✅ Connected successfully!');
        
        console.log('📁 Listing root directory...');
        const listResult = await window.api.ssh.listDirectory('/');
        
        if (listResult.success) {
          console.log('✅ Directory listing successful!');
          console.log(`📊 Found ${listResult.files.length} items`);
          
          // Show first few files/directories
          console.table(listResult.files.slice(0, 10));
          
          console.log('💻 Testing command execution...');
          const cmdResult = await window.api.ssh.executeCommand('pwd && whoami');
          
          if (cmdResult.success) {
            console.log('✅ Command executed successfully!');
            console.log('📤 Output:', cmdResult.stdout);
            console.log('🔢 Exit code:', cmdResult.exitCode);
          } else {
            console.log('❌ Command failed:', cmdResult.message);
          }
          
        } else {
          console.log('❌ Directory listing failed:', listResult.message);
        }
        
        console.log('🔌 Disconnecting...');
        await window.api.ssh.disconnect();
        console.log('✅ Disconnected successfully!');
        
      } else {
        console.log('❌ Connection failed:', connectResult.message);
      }
      
    } else {
      console.log('❌ Connection test failed:', testResult.message);
    }
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
  
  console.log('🏁 Test completed!');
}

// Instructions for use
console.log('🔑 SSH Test Script for ssh.academy.psychscene.com loaded!');
console.log('');
console.log('📋 To use this script:');
console.log('1. Edit the privateKey field above with your actual private key');
console.log('2. Run: testPsychSceneSSH()');
console.log('');
console.log('💡 To get your private key:');
console.log('- Windows: Run the find-ssh-key.ps1 script');
console.log('- Linux/Mac: cat ~/.ssh/id_rsa (or your key file)');
console.log('');
console.log('🎯 Expected format:');
console.log('-----BEGIN OPENSSH PRIVATE KEY-----');
console.log('b3BlbnNzaC1rZXktdjEAAAAA...');
console.log('-----END OPENSSH PRIVATE KEY-----');
