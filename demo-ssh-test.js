// Demo script to test SSH functionality programmatically
// This can be run in the browser console when the app is running

async function testSSHConnection() {
  console.log('🚀 Starting SSH Connection Test...');
  
  // Example SSH configuration - replace with your actual server details
  const sshConfig = {
    host: 'your-server.com',  // Replace with your SSH server
    port: 22,
    username: 'your-username', // Replace with your username
    password: 'your-password', // Replace with your password
    timeout: 10
  };
  
  try {
    console.log('📡 Testing connection...');
    const testResult = await window.api.ssh.testConnection(sshConfig);
    
    if (testResult.success) {
      console.log('✅ Connection test successful!');
      console.log('Server Info:', testResult.serverInfo);
      
      console.log('🔗 Establishing persistent connection...');
      const connectResult = await window.api.ssh.connect(sshConfig);
      
      if (connectResult.success) {
        console.log('✅ Connected successfully!');
        
        console.log('📁 Listing root directory...');
        const listResult = await window.api.ssh.listDirectory('/');
        
        if (listResult.success) {
          console.log('✅ Directory listing successful!');
          console.log('Files found:', listResult.files.length);
          console.table(listResult.files.slice(0, 10)); // Show first 10 files
          
          console.log('💻 Executing test command...');
          const cmdResult = await window.api.ssh.executeCommand('ls -la /');
          
          if (cmdResult.success) {
            console.log('✅ Command executed successfully!');
            console.log('Exit code:', cmdResult.exitCode);
            console.log('Output:', cmdResult.stdout.substring(0, 200) + '...');
          } else {
            console.log('❌ Command execution failed:', cmdResult.message);
          }
          
        } else {
          console.log('❌ Directory listing failed:', listResult.message);
        }
        
        console.log('🔌 Disconnecting...');
        const disconnectResult = await window.api.ssh.disconnect();
        
        if (disconnectResult.success) {
          console.log('✅ Disconnected successfully!');
        } else {
          console.log('❌ Disconnect failed:', disconnectResult.message);
        }
        
      } else {
        console.log('❌ Connection failed:', connectResult.message);
      }
      
    } else {
      console.log('❌ Connection test failed:', testResult.message);
    }
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
  
  console.log('🏁 SSH test completed!');
}

// Example usage with different authentication methods
const examples = {
  // Password authentication
  passwordAuth: {
    host: 'example.com',
    port: 22,
    username: 'user',
    password: 'password123',
    timeout: 10
  },
  
  // Private key authentication
  keyAuth: {
    host: 'example.com',
    port: 22,
    username: 'user',
    privateKey: `-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
...your private key content...
-----END OPENSSH PRIVATE KEY-----`,
    passphrase: 'optional-passphrase',
    timeout: 10
  },
  
  // Local SSH server (for testing)
  localhost: {
    host: 'localhost',
    port: 22,
    username: 'your-local-username',
    password: 'your-local-password',
    timeout: 5
  }
};

console.log('SSH Test Demo Script Loaded!');
console.log('Available functions:');
console.log('- testSSHConnection() - Run full SSH test');
console.log('- examples - Sample SSH configurations');
console.log('');
console.log('To test, update the sshConfig in testSSHConnection() with your server details and run:');
console.log('testSSHConnection()');
