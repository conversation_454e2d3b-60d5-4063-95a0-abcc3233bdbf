import { ElectronAPI } from '@electron-toolkit/preload'

interface SSHConnectionConfig {
  host: string
  port: number
  username: string
  password?: string
  privateKey?: Buffer | string
  passphrase?: string
  timeout?: number
}

interface SSHFileInfo {
  name: string
  type: 'file' | 'directory' | 'symlink'
  size: number
  modifyTime: Date
  accessTime: Date
  permissions: string
  owner: string
  group: string
}

interface SSHConnectionResult {
  success: boolean
  message: string
  serverInfo?: {
    serverVersion: string
    algorithms: any
  }
}

interface SSHAPI {
  testConnection: (config: SSHConnectionConfig) => Promise<SSHConnectionResult>
  connect: (config: SSHConnectionConfig) => Promise<SSHConnectionResult>
  disconnect: () => Promise<{ success: boolean; message: string }>
  listDirectory: (
    remotePath?: string
  ) => Promise<{ success: boolean; files: SSHFileInfo[]; message?: string }>
  executeCommand: (
    command: string
  ) => Promise<{
    success: boolean
    stdout: string
    stderr: string
    exitCode: number
    message?: string
  }>
  getStatus: () => Promise<{
    connected: boolean
    connectionInfo: Partial<SSHConnectionConfig> | null
  }>
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      ssh: SSHAPI
    }
  }
}
