# SSH Key Setup Guide for ssh.academy.psychscene.com

## 🔍 **Step 1: Find Your Private Key**

Since your SSH command `ssh <EMAIL> -p 18765` works, your private key is already set up. Here's how to find it:

### **Windows:**
```powershell
# Check default SSH key locations
dir $env:USERPROFILE\.ssh\
# Look for files like: id_rsa, id_ed25519, id_ecdsa (without .pub extension)
```

### **Linux/macOS:**
```bash
# Check default SSH key locations
ls -la ~/.ssh/
# Look for files like: id_rsa, id_ed25519, id_ecdsa (without .pub extension)
```

### **Common private key files:**
- `id_rsa` (RSA key)
- `id_ed25519` (Ed25519 key - recommended)
- `id_ecdsa` (ECDSA key)
- Custom named keys

## 📋 **Step 2: Copy Your Private Key Content**

### **Windows (PowerShell):**
```powershell
# Replace 'id_rsa' with your actual key filename
Get-Content $env:USERPROFILE\.ssh\id_rsa | clip
# This copies the key to clipboard
```

### **Linux/macOS:**
```bash
# Replace 'id_rsa' with your actual key filename
cat ~/.ssh/id_rsa
# Copy the output manually
```

### **Alternative - Use Notepad/Text Editor:**
1. Navigate to your `.ssh` folder
2. Open your private key file with a text editor
3. Copy all content (including the BEGIN/END lines)

## 🖥️ **Step 3: Configure the App**

1. **Start the application:**
   ```bash
   npm run dev
   ```

2. **Fill in the SSH Connection Form:**
   - **Host:** `ssh.academy.psychscene.com`
   - **Port:** `18765`
   - **Username:** `user` (or your actual username)
   - **Authentication Method:** Select "Private Key"
   - **Private Key:** Paste your private key content here
   - **Passphrase:** Enter if your key has a passphrase (leave empty if not)
   - **Timeout:** `10` seconds (default)

3. **Test the connection:**
   - Click "Test Connection" first
   - If successful, click "Connect"
   - Browse remote files in the file browser

## 🔑 **Example Private Key Format**

Your private key should look like this:

```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW
QyNTUxOQAAACBK8B9C9W8P5Q7X2Y3Z4R5T6S8N1M2L3K4J5H6G7F8D9A0B1CAAAAA5
... (many lines of encoded key data) ...
-----END OPENSSH PRIVATE KEY-----
```

Or for RSA keys:
```
-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA1234567890abcdef...
... (many lines of encoded key data) ...
-----END RSA PRIVATE KEY-----
```

## 🛠️ **Troubleshooting**

### **If you get "Permission denied":**
1. Verify the username is correct
2. Check if your public key is properly added to the server
3. Ensure you're using the correct private key

### **If you get "Connection timeout":**
1. Check the host and port are correct
2. Verify your internet connection
3. Try increasing the timeout value

### **If you get "Host key verification failed":**
- The app automatically accepts host keys for development
- This shouldn't be an issue

### **To verify your setup works:**
Run this in your terminal first:
```bash
ssh <EMAIL> -p 18765 -v
```
The `-v` flag shows verbose output to help debug any issues.

## 🚀 **Quick Test Script**

Once you have your private key, you can test it programmatically by opening the browser console (F12) and running:

```javascript
// Test your SSH connection
const testConfig = {
  host: 'ssh.academy.psychscene.com',
  port: 18765,
  username: 'user',
  privateKey: `-----BEGIN OPENSSH PRIVATE KEY-----
... paste your private key here ...
-----END OPENSSH PRIVATE KEY-----`,
  timeout: 10
};

// Test the connection
window.api.ssh.testConnection(testConfig).then(result => {
  console.log('Test result:', result);
  if (result.success) {
    console.log('✅ Connection successful!');
    console.log('Server info:', result.serverInfo);
  } else {
    console.log('❌ Connection failed:', result.message);
  }
});
```

## 📝 **Notes**

- Keep your private key secure and never share it
- The app stores the key temporarily in memory only
- Your private key is never sent over the network (only used for authentication)
- The public key should already be in your server's `~/.ssh/authorized_keys` file

## 🎯 **Expected Result**

After successful connection, you should see:
- ✅ Green success message
- Server information displayed
- File browser showing remote directory contents
- Ability to navigate directories by double-clicking

The connection should work exactly like your command line SSH, but with a graphical interface!
