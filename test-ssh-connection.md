# SSH Connection Testing Guide

## Overview
The Site Cloner Desktop application now includes comprehensive SSH connectivity functionality. Here's how to test and use the SSH features:

## Features Implemented

### 1. SSH Connection Form
- **Host**: Enter the SSH server hostname or IP address
- **Port**: SSH port (default: 22)
- **Username**: SSH username
- **Authentication Methods**:
  - Password authentication
  - Private key authentication (with optional passphrase)
- **Connection Timeout**: Configurable timeout (5-60 seconds)

### 2. Connection Testing
- **Test Connection**: Verify SSH credentials without maintaining connection
- **Connect**: Establish persistent SSH connection
- **Disconnect**: Close SSH connection

### 3. Remote File Browser
- **Directory Listing**: Browse remote files and directories
- **File Information**: View file size, permissions, and type
- **Navigation**: Double-click directories to navigate
- **Refresh**: Update directory contents

### 4. Command Execution
- Execute commands on remote server
- View stdout, stderr, and exit codes

## Testing Instructions

### Prerequisites
You'll need access to an SSH server to test the functionality. Options include:

1. **Local SSH Server** (recommended for testing):
   - Install OpenSSH Server on Windows/Linux/macOS
   - Use localhost as the host

2. **Cloud Server**:
   - AWS EC2, DigitalOcean, Linode, etc.
   - VPS with SSH access

3. **Docker SSH Container**:
   ```bash
   docker run -d -p 2222:22 --name ssh-test \
     -e SSH_ENABLE_PASSWORD_AUTH=true \
     -e USER_NAME=testuser \
     -e USER_PASSWORD=testpass \
     lscr.io/linuxserver/openssh-server:latest
   ```

### Test Cases

#### 1. Password Authentication Test
1. Open the application
2. Navigate to "SSH Connection" tab
3. Fill in the form:
   - Host: your-ssh-server.com (or localhost)
   - Port: 22
   - Username: your-username
   - Authentication Method: Password
   - Password: your-password
4. Click "Test Connection" to verify credentials
5. Click "Connect" to establish connection
6. Verify the file browser appears with remote files

#### 2. Private Key Authentication Test
1. Use the same form but select "Private Key" authentication
2. Paste your private key in the text area
3. Enter passphrase if required
4. Test and connect

#### 3. File Browser Test
1. After successful connection, verify:
   - Files and directories are listed
   - Icons show correct file types (📁 for directories, 📄 for files)
   - File sizes and permissions are displayed
   - Double-clicking directories navigates into them
   - Refresh button updates the listing

#### 4. Error Handling Test
1. Test with invalid credentials (should show error message)
2. Test with unreachable host (should timeout gracefully)
3. Test connection timeout (set low timeout value)

## Example SSH Server Setup (Docker)

For quick testing, you can use this Docker command to create a test SSH server:

```bash
# Create SSH test server
docker run -d \
  --name ssh-test-server \
  -p 2222:22 \
  -e PUID=1000 \
  -e PGID=1000 \
  -e TZ=Etc/UTC \
  -e PUBLIC_KEY_FILE=/path/to/your/key.pub \
  -e SUDO_ACCESS=false \
  -e PASSWORD_ACCESS=true \
  -e USER_PASSWORD=testpassword123 \
  -e USER_NAME=testuser \
  -v /path/to/config:/config \
  lscr.io/linuxserver/openssh-server:latest
```

Then test with:
- Host: localhost
- Port: 2222
- Username: testuser
- Password: testpassword123

## Expected Results

### Successful Connection
- Green success message with server information
- File browser appears showing remote directory contents
- Files are sortable (directories first, then files alphabetically)
- Navigation works by double-clicking directories

### Failed Connection
- Red error message with specific error details
- Form remains editable for corrections
- No file browser appears

### File Operations
- Directory listing shows file types, sizes, and permissions
- Navigation updates the current path display
- Refresh button reloads current directory

## Troubleshooting

### Common Issues
1. **Connection Timeout**: Increase timeout value or check network connectivity
2. **Authentication Failed**: Verify username/password or private key
3. **Permission Denied**: Check SSH server configuration and user permissions
4. **Host Key Verification**: The app automatically accepts host keys (for development)

### Debug Information
- Check the DevTools console (F12) for detailed error messages
- Server information is displayed on successful connection
- Connection status is maintained and displayed

## Next Steps

The SSH functionality is now ready for integration with the site cloning features. You can extend this by:

1. Adding file download/upload capabilities
2. Integrating with website cloning workflows
3. Adding SSH tunnel support for database connections
4. Implementing batch file operations

The foundation is solid and ready for your specific use cases!
