<template>
  <div class="ssh-connection-form">
    <h2>SSH Connection</h2>
    
    <form @submit.prevent="handleSubmit" class="connection-form">
      <div class="form-group">
        <label for="host">Host *</label>
        <input
          id="host"
          v-model="formData.host"
          type="text"
          placeholder="example.com or *************"
          required
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div class="form-group">
        <label for="port">Port</label>
        <input
          id="port"
          v-model.number="formData.port"
          type="number"
          min="1"
          max="65535"
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div class="form-group">
        <label for="username">Username *</label>
        <input
          id="username"
          v-model="formData.username"
          type="text"
          placeholder="root, ubuntu, etc."
          required
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div class="form-group">
        <label for="auth-method">Authentication Method</label>
        <select
          id="auth-method"
          v-model="authMethod"
          :disabled="isConnecting || isConnected"
        >
          <option value="password">Password</option>
          <option value="key">Private Key</option>
        </select>
      </div>

      <div v-if="authMethod === 'password'" class="form-group">
        <label for="password">Password *</label>
        <input
          id="password"
          v-model="formData.password"
          type="password"
          placeholder="Enter password"
          :required="authMethod === 'password'"
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div v-if="authMethod === 'key'" class="form-group">
        <label for="private-key">Private Key *</label>
        <textarea
          id="private-key"
          v-model="formData.privateKey"
          placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
          rows="6"
          :required="authMethod === 'key'"
          :disabled="isConnecting || isConnected"
        ></textarea>
      </div>

      <div v-if="authMethod === 'key'" class="form-group">
        <label for="passphrase">Passphrase (if required)</label>
        <input
          id="passphrase"
          v-model="formData.passphrase"
          type="password"
          placeholder="Enter passphrase for private key"
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div class="form-group">
        <label for="timeout">Connection Timeout (seconds)</label>
        <input
          id="timeout"
          v-model.number="formData.timeout"
          type="number"
          min="5"
          max="120"
          :disabled="isConnecting || isConnected"
        />
      </div>

      <div class="form-actions">
        <button
          type="button"
          @click="testConnection"
          :disabled="isConnecting || !isFormValid"
          class="btn btn-secondary"
        >
          {{ isConnecting ? 'Testing...' : 'Test Connection' }}
        </button>
        
        <button
          v-if="!isConnected"
          type="submit"
          :disabled="isConnecting || !isFormValid"
          class="btn btn-primary"
        >
          {{ isConnecting ? 'Connecting...' : 'Connect' }}
        </button>
        
        <button
          v-if="isConnected"
          type="button"
          @click="disconnect"
          class="btn btn-danger"
        >
          Disconnect
        </button>
      </div>
    </form>

    <div v-if="connectionStatus" class="connection-status" :class="connectionStatus.type">
      <p>{{ connectionStatus.message }}</p>
      <div v-if="connectionStatus.serverInfo" class="server-info">
        <h4>Server Information:</h4>
        <p><strong>Version:</strong> {{ connectionStatus.serverInfo.serverVersion }}</p>
      </div>
    </div>

    <div v-if="isConnected && currentPath" class="file-browser">
      <h3>Remote Files</h3>
      <div class="current-path">
        <strong>Current Path:</strong> {{ currentPath }}
        <button @click="refreshDirectory" class="btn btn-small">Refresh</button>
      </div>
      
      <div v-if="isLoadingFiles" class="loading">Loading files...</div>
      
      <div v-if="files.length > 0" class="file-list">
        <div
          v-for="file in files"
          :key="file.name"
          class="file-item"
          :class="file.type"
          @dblclick="navigateToFile(file)"
        >
          <span class="file-icon">{{ getFileIcon(file.type) }}</span>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">{{ formatFileSize(file.size) }}</span>
          <span class="file-permissions">{{ file.permissions }}</span>
        </div>
      </div>
      
      <div v-if="files.length === 0 && !isLoadingFiles" class="no-files">
        No files found in this directory.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface SSHConnectionConfig {
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  passphrase?: string
  timeout?: number
}

interface SSHFileInfo {
  name: string
  type: 'file' | 'directory' | 'symlink'
  size: number
  modifyTime: Date
  accessTime: Date
  permissions: string
  owner: string
  group: string
}

// Form data
const formData = ref<SSHConnectionConfig>({
  host: '',
  port: 22,
  username: '',
  password: '',
  privateKey: '',
  passphrase: '',
  timeout: 10
})

const authMethod = ref<'password' | 'key'>('password')
const isConnecting = ref(false)
const isConnected = ref(false)
const connectionStatus = ref<{
  type: 'success' | 'error' | 'info'
  message: string
  serverInfo?: any
} | null>(null)

// File browser
const currentPath = ref('/')
const files = ref<SSHFileInfo[]>([])
const isLoadingFiles = ref(false)

// Computed properties
const isFormValid = computed(() => {
  const basic = formData.value.host && formData.value.username
  if (authMethod.value === 'password') {
    return basic && formData.value.password
  } else {
    return basic && formData.value.privateKey
  }
})

// Methods
const testConnection = async () => {
  if (!isFormValid.value) return
  
  isConnecting.value = true
  connectionStatus.value = null
  
  try {
    const config = getConnectionConfig()
    const result = await window.api.ssh.testConnection(config)
    
    connectionStatus.value = {
      type: result.success ? 'success' : 'error',
      message: result.message,
      serverInfo: result.serverInfo
    }
  } catch (error) {
    connectionStatus.value = {
      type: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  } finally {
    isConnecting.value = false
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return
  
  isConnecting.value = true
  connectionStatus.value = null
  
  try {
    const config = getConnectionConfig()
    const result = await window.api.ssh.connect(config)
    
    if (result.success) {
      isConnected.value = true
      connectionStatus.value = {
        type: 'success',
        message: result.message,
        serverInfo: result.serverInfo
      }
      await loadDirectory('/')
    } else {
      connectionStatus.value = {
        type: 'error',
        message: result.message
      }
    }
  } catch (error) {
    connectionStatus.value = {
      type: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  } finally {
    isConnecting.value = false
  }
}

const disconnect = async () => {
  try {
    await window.api.ssh.disconnect()
    isConnected.value = false
    files.value = []
    currentPath.value = '/'
    connectionStatus.value = {
      type: 'info',
      message: 'Disconnected successfully'
    }
  } catch (error) {
    connectionStatus.value = {
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to disconnect'
    }
  }
}

const loadDirectory = async (path: string) => {
  isLoadingFiles.value = true
  try {
    const result = await window.api.ssh.listDirectory(path)
    if (result.success) {
      files.value = result.files.sort((a, b) => {
        // Directories first, then files
        if (a.type === 'directory' && b.type !== 'directory') return -1
        if (a.type !== 'directory' && b.type === 'directory') return 1
        return a.name.localeCompare(b.name)
      })
      currentPath.value = path
    } else {
      connectionStatus.value = {
        type: 'error',
        message: result.message || 'Failed to load directory'
      }
    }
  } catch (error) {
    connectionStatus.value = {
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to load directory'
    }
  } finally {
    isLoadingFiles.value = false
  }
}

const refreshDirectory = () => {
  loadDirectory(currentPath.value)
}

const navigateToFile = (file: SSHFileInfo) => {
  if (file.type === 'directory') {
    const newPath = currentPath.value === '/' 
      ? `/${file.name}` 
      : `${currentPath.value}/${file.name}`
    loadDirectory(newPath)
  }
}

const getConnectionConfig = (): SSHConnectionConfig => {
  const config: SSHConnectionConfig = {
    host: formData.value.host,
    port: formData.value.port,
    username: formData.value.username,
    timeout: formData.value.timeout
  }
  
  if (authMethod.value === 'password') {
    config.password = formData.value.password
  } else {
    config.privateKey = formData.value.privateKey
    if (formData.value.passphrase) {
      config.passphrase = formData.value.passphrase
    }
  }
  
  return config
}

const getFileIcon = (type: string): string => {
  switch (type) {
    case 'directory': return '📁'
    case 'symlink': return '🔗'
    default: return '📄'
  }
}

const formatFileSize = (size: number): string => {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// Check connection status on mount
onMounted(async () => {
  try {
    const status = await window.api.ssh.getStatus()
    isConnected.value = status.connected
    if (status.connected && status.connectionInfo) {
      // Only copy compatible properties to avoid type issues
      const { host, port, username, timeout } = status.connectionInfo
      formData.value = {
        ...formData.value,
        host: host || '',
        port: port || 22,
        username: username || '',
        timeout: timeout || 10
      }
      await loadDirectory('/')
    }
  } catch (error) {
    console.error('Failed to check SSH status:', error)
  }
})
</script>

<style scoped>
.ssh-connection-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ssh-connection-form h2 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.connection-form {
  display: grid;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-small {
  padding: 5px 10px;
  font-size: 12px;
  margin-left: 10px;
}

.connection-status {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid;
}

.connection-status.success {
  background-color: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.connection-status.error {
  background-color: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.connection-status.info {
  background-color: #d1ecf1;
  border-color: #17a2b8;
  color: #0c5460;
}

.server-info {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.server-info h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
}

.file-browser {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #dee2e6;
}

.file-browser h3 {
  color: #333;
  margin-bottom: 15px;
}

.current-path {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #e9ecef;
  border-radius: 4px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.file-list {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.file-item {
  display: grid;
  grid-template-columns: 30px 1fr 80px 80px;
  gap: 10px;
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
  cursor: pointer;
  transition: background-color 0.2s;
  align-items: center;
}

.file-item:hover {
  background-color: #f8f9fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.directory {
  font-weight: 600;
}

.file-icon {
  font-size: 16px;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size,
.file-permissions {
  font-size: 12px;
  color: #666;
  text-align: right;
}

.no-files {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

@media (max-width: 600px) {
  .ssh-connection-form {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .file-item {
    grid-template-columns: 30px 1fr;
    gap: 5px;
  }

  .file-size,
  .file-permissions {
    display: none;
  }
}
</style>
