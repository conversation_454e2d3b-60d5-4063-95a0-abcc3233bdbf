# Test your SSH connection from command line
# This will help verify the connection works before using the app

Write-Host "🔍 Testing SSH Connection" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

$host = "ssh.academy.psychscene.com"
$port = "18765"
$username = "u7-rmvjbo31ek67"

Write-Host "Testing connection to: $username@$host -p $port" -ForegroundColor Yellow
Write-Host ""

Write-Host "💡 Running SSH command..." -ForegroundColor Green
Write-Host "Command: ssh $username@$host -p $port -o ConnectTimeout=30 -v" -ForegroundColor Gray
Write-Host ""

# Test the SSH connection with verbose output
try {
    $result = ssh $username@$host -p $port -o ConnectTimeout=30 -v -o BatchMode=yes -o StrictHostKeyChecking=no whoami 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH connection successful!" -ForegroundColor Green
        Write-Host "Output: $result" -ForegroundColor White
    } else {
        Write-Host "❌ SSH connection failed!" -ForegroundColor Red
        Write-Host "Error output:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error running SSH command: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Make sure OpenSSH client is installed:" -ForegroundColor Yellow
    Write-Host "   - Windows 10/11: Should be installed by default" -ForegroundColor Gray
    Write-Host "   - Or install Git Bash which includes SSH" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🔧 If the command line works but the app doesn't:" -ForegroundColor Cyan
Write-Host "1. Increase timeout in the app to 30-60 seconds" -ForegroundColor White
Write-Host "2. Check if your private key format is correct" -ForegroundColor White
Write-Host "3. Try the connection test again" -ForegroundColor White

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
