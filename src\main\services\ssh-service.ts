import { Client, ConnectConfig, SFTPWrapper } from 'ssh2'
import { EventEmitter } from 'events'

export interface SSHConnectionConfig {
  host: string
  port: number
  username: string
  password?: string
  privateKey?: Buffer | string
  passphrase?: string
  timeout?: number
}

export interface SSHFileInfo {
  name: string
  type: 'file' | 'directory' | 'symlink'
  size: number
  modifyTime: Date
  accessTime: Date
  permissions: string
  owner: string
  group: string
}

export interface SSHConnectionResult {
  success: boolean
  message: string
  serverInfo?: {
    serverVersion: string
    algorithms: any
  }
}

export class SSHService extends EventEmitter {
  private client: Client | null = null
  private sftp: SFTPWrapper | null = null
  private isConnected = false
  private connectionConfig: SSHConnectionConfig | null = null

  constructor() {
    super()
  }

  /**
   * Test SSH connection without maintaining it
   */
  async testConnection(config: SSHConnectionConfig): Promise<SSHConnectionResult> {
    return new Promise((resolve) => {
      const testClient = new Client()

      console.log(
        `[SSH] Testing connection to ${config.host}:${config.port} with user ${config.username}`
      )

      const timeoutMs = (config.timeout || 10) * 1000 // Convert seconds to milliseconds
      console.log(`[SSH] Timeout set to ${timeoutMs}ms (${config.timeout || 10} seconds)`)

      const timeout = setTimeout(() => {
        console.log('[SSH] Connection timeout reached')
        testClient.destroy()
        resolve({
          success: false,
          message: `Connection timeout after ${config.timeout || 10} seconds`
        })
      }, timeoutMs)

      testClient.on('ready', () => {
        clearTimeout(timeout)
        const serverInfo = {
          serverVersion: (testClient as any)._server_ident_raw || 'Unknown',
          algorithms: (testClient as any)._kex || {}
        }

        testClient.end()
        resolve({
          success: true,
          message: 'Connection successful',
          serverInfo
        })
      })

      testClient.on('error', (err) => {
        console.log('[SSH] Connection error:', err.message)
        clearTimeout(timeout)
        testClient.destroy()
        resolve({
          success: false,
          message: err.message || 'Connection failed'
        })
      })

      const connectConfig: ConnectConfig = {
        host: config.host,
        port: config.port,
        username: config.username,
        readyTimeout: config.timeout || 10000,
        ...(config.password && { password: config.password }),
        ...(config.privateKey && { privateKey: config.privateKey }),
        ...(config.passphrase && { passphrase: config.passphrase })
      }

      testClient.connect(connectConfig)
    })
  }

  /**
   * Connect to SSH server and maintain connection
   */
  async connect(config: SSHConnectionConfig): Promise<SSHConnectionResult> {
    if (this.isConnected) {
      await this.disconnect()
    }

    return new Promise((resolve) => {
      this.client = new Client()
      this.connectionConfig = config

      const timeoutMs = (config.timeout || 10) * 1000 // Convert seconds to milliseconds
      const timeout = setTimeout(() => {
        this.client?.destroy()
        this.client = null
        resolve({
          success: false,
          message: `Connection timeout after ${config.timeout || 10} seconds`
        })
      }, timeoutMs)

      this.client.on('ready', () => {
        clearTimeout(timeout)
        this.isConnected = true

        const serverInfo = {
          serverVersion: (this.client as any)!._server_ident_raw || 'Unknown',
          algorithms: (this.client as any)!._kex || {}
        }

        this.emit('connected', serverInfo)
        resolve({
          success: true,
          message: 'Connected successfully',
          serverInfo
        })
      })

      this.client.on('error', (err) => {
        clearTimeout(timeout)
        this.isConnected = false
        this.client?.destroy()
        this.client = null

        this.emit('error', err)
        resolve({
          success: false,
          message: err.message || 'Connection failed'
        })
      })

      this.client.on('close', () => {
        this.isConnected = false
        this.sftp = null
        this.emit('disconnected')
      })

      const connectConfig: ConnectConfig = {
        host: config.host,
        port: config.port,
        username: config.username,
        readyTimeout: config.timeout || 10000,
        ...(config.password && { password: config.password }),
        ...(config.privateKey && { privateKey: config.privateKey }),
        ...(config.passphrase && { passphrase: config.passphrase })
      }

      this.client.connect(connectConfig)
    })
  }

  /**
   * Disconnect from SSH server
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      this.client.end()
      this.client = null
    }
    this.sftp = null
    this.isConnected = false
    this.connectionConfig = null
  }

  /**
   * Get SFTP connection
   */
  private async getSFTP(): Promise<SFTPWrapper> {
    if (!this.isConnected || !this.client) {
      throw new Error('Not connected to SSH server')
    }

    if (this.sftp) {
      return this.sftp
    }

    return new Promise((resolve, reject) => {
      this.client!.sftp((err, sftp) => {
        if (err) {
          reject(new Error(`Failed to create SFTP session: ${err.message}`))
          return
        }

        this.sftp = sftp
        resolve(sftp)
      })
    })
  }

  /**
   * List files and directories in remote path
   */
  async listDirectory(remotePath: string = '/'): Promise<SSHFileInfo[]> {
    const sftp = await this.getSFTP()

    return new Promise((resolve, reject) => {
      sftp.readdir(remotePath, (err, list) => {
        if (err) {
          reject(new Error(`Failed to list directory: ${err.message}`))
          return
        }

        const fileInfos: SSHFileInfo[] = list.map((item) => ({
          name: item.filename,
          type: item.attrs.isDirectory()
            ? 'directory'
            : item.attrs.isSymbolicLink()
              ? 'symlink'
              : 'file',
          size: item.attrs.size || 0,
          modifyTime: new Date((item.attrs.mtime || 0) * 1000),
          accessTime: new Date((item.attrs.atime || 0) * 1000),
          permissions: item.attrs.mode
            ? '0' + (item.attrs.mode & parseInt('777', 8)).toString(8)
            : '000',
          owner: item.attrs.uid?.toString() || 'unknown',
          group: item.attrs.gid?.toString() || 'unknown'
        }))

        resolve(fileInfos)
      })
    })
  }

  /**
   * Execute command on remote server
   */
  async executeCommand(
    command: string
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    if (!this.isConnected || !this.client) {
      throw new Error('Not connected to SSH server')
    }

    return new Promise((resolve, reject) => {
      this.client!.exec(command, (err, stream) => {
        if (err) {
          reject(new Error(`Failed to execute command: ${err.message}`))
          return
        }

        let stdout = ''
        let stderr = ''

        stream.on('close', (code: number) => {
          resolve({
            stdout,
            stderr,
            exitCode: code || 0
          })
        })

        stream.on('data', (data: Buffer) => {
          stdout += data.toString()
        })

        stream.stderr.on('data', (data: Buffer) => {
          stderr += data.toString()
        })
      })
    })
  }

  /**
   * Check if connected
   */
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  /**
   * Get current connection config (without sensitive data)
   */
  getConnectionInfo(): Partial<SSHConnectionConfig> | null {
    if (!this.connectionConfig) {
      return null
    }

    return {
      host: this.connectionConfig.host,
      port: this.connectionConfig.port,
      username: this.connectionConfig.username,
      timeout: this.connectionConfig.timeout
    }
  }
}

// Singleton instance
export const sshService = new SSHService()
